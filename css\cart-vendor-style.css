/* AliExpress-style Cart with Vendor Grouping */

/* Override default WooCommerce cart table styles */
.woocommerce-cart-form .shop_table.cart {
  display: none !important;
}

.woocommerce-cart-form .shop_table.cart thead,
.woocommerce-cart-form .shop_table.cart tbody {
  display: none !important;
}

/* Vendor Cart Section */
.vendor-cart-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  display: block !important;
}

.vendor-cart-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Vendor Header */
.vendor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.vendor-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.vendor-select-all {
  transform: scale(1.2);
  margin: 0;
}

.vendor-details .vendor-name {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d2939;
  display: flex;
  align-items: center;
  gap: 8px;
}

.vendor-details .vendor-name a {
  color: #1d2939;
  text-decoration: none;
  transition: color 0.3s ease;
}

.vendor-details .vendor-name a:hover {
  color: #ea9c00;
}

.vendor-details .vendor-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vendor-details .vendor-item-count {
  font-size: 0.9rem;
  color: #667085;
}

/* Shipping Selection Button */
.shipping-selection-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  color: #1d2939;
  text-align: left;
  width: 100%;
  max-width: 280px;
}

.shipping-selection-btn:hover {
  background: #e9ecef;
  border-color: #ea9c00;
}

.shipping-selection-btn .shipping-text {
  font-weight: 500;
  flex: 1;
}

.shipping-selection-btn .shipping-cost {
  font-weight: 600;
  color: #ea9c00;
}

.shipping-selection-btn .feather-sm {
  width: 14px;
  height: 14px;
}

.vendor-actions .vendor-delete-all {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #f04438;
  border-radius: 8px;
  color: #f04438;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.vendor-actions .vendor-delete-all:hover {
  background: #f04438;
  color: white;
}

/* Vendor Products */
.vendor-products {
  padding: 0;
}

.cart-product-item {
  display: flex;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  transition: all 0.3s ease;
  background: white;
}

.cart-product-item:last-child {
  border-bottom: none;
}

.cart-product-item:hover {
  background-color: #fafafa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.cart-product-item .product-select {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.cart-product-item .product-select input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.cart-product-item .product-info {
  flex: 1;
  display: flex !important;
  gap: 20px;
  align-items: center;
  min-width: 0;
}

.cart-product-item .product-thumbnail {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.cart-product-item .product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-product-item .product-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cart-product-item .product-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1d2939;
  text-decoration: none;
  line-height: 1.4;
  margin: 0;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.cart-product-item .product-name:hover {
  color: #ea9c00;
}

.cart-product-item .vendor-store-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: #667085;
  margin: 4px 0;
}

.cart-product-item .vendor-store-info i {
  width: 14px;
  height: 14px;
}

.cart-product-item .product-reviews {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #667085;
}

.cart-product-item .product-reviews .star-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cart-product-item .product-reviews .rating {
  font-weight: 600;
  color: #ea9c00;
}

.cart-product-item .product-price-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  margin-left: 20px;
}

.cart-product-item .product-subtotal {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1d2939;
  margin: 0;
}

.cart-product-item .product-tax {
  font-size: 0.85rem;
  color: #667085;
  margin: 0;
}

.cart-product-item .quantity-control {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}

.cart-product-item .quantity-control button {
  width: 32px;
  height: 32px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #667085;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.cart-product-item .quantity-control button:hover {
  background: #ea9c00;
  color: white;
  border-color: #ea9c00;
}

.cart-product-item .quantity-control .qty-input {
  width: 50px;
  height: 32px;
  text-align: center;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  background: white;
}

.cart-product-item .quantity-control .qty-input:focus {
  outline: none;
  border-color: #ea9c00;
  box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
}

.cart-product-item .product-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 20px;
}

.cart-product-item .remove-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid #f04438;
  border-radius: 8px;
  color: #f04438;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cart-product-item .remove-item:hover {
  background: #f04438;
  color: white;
  transform: translateY(-1px);
}

.cart-product-item .remove-item i {
  width: 14px;
  height: 14px;
}

/* Enhanced Product Card Styling */
.cart-product-item {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  margin-bottom: 1px;
}

.cart-product-item .product-name {
  font-weight: 500;
  color: #374151;
  line-height: 1.3;
}

.cart-product-item .vendor-store-info {
  color: #6b7280;
  font-size: 0.8rem;
}

.cart-product-item .product-reviews .star-rating {
  gap: 2px;
}

.cart-product-item .product-reviews .rating {
  font-size: 0.85rem;
  margin-left: 4px;
}

.cart-product-item .product-reviews .review-count {
  font-size: 0.8rem;
  color: #9ca3af;
}

.cart-product-item .product-reviews .no-reviews {
  font-size: 0.8rem;
  color: #9ca3af;
  font-style: italic;
  margin-left: 4px;
}

.cart-product-item .product-subtotal {
  font-size: 1.1rem;
  font-weight: 700;
  color: #111827;
}

.cart-product-item .product-tax {
  font-size: 0.8rem;
  color: #6b7280;
  font-weight: 400;
}

/* Quantity Control Enhancements */
.cart-product-item .quantity-control {
  background: #f9fafb;
  border-radius: 8px;
  padding: 4px;
  border: 1px solid #e5e7eb;
  width: fit-content;
}

.cart-product-item .quantity-control button {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.cart-product-item .quantity-control button:hover {
  background: #ea9c00;
  border-color: #ea9c00;
  color: white;
}

.cart-product-item .quantity-control .qty-input {
  border: none;
  background: transparent;
  color: #111827;
  font-weight: 600;
}

.cart-product-item .quantity-control .qty-input:focus {
  outline: none;
  box-shadow: none;
}

/* Product Actions Styling */
.cart-product-item .remove-item {
  font-size: 0.8rem;
  padding: 6px 10px;
  border-color: #ef4444;
  color: #ef4444;
}

.cart-product-item .remove-item:hover {
  background: #ef4444;
  color: white;
}

/* Vendor Shipping Section */
.vendor-shipping-section {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.vendor-shipping-section .shipping-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 15px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1d2939;
}

.shipping-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shipping-method-option {
  display: flex;
  align-items: center;
}

.shipping-method-option input[type="radio"] {
  margin-right: 12px;
  transform: scale(1.1);
}

.shipping-method-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shipping-method-label:hover {
  border-color: #ea9c00;
  background: #fef7e6;
}

.shipping-method-option input[type="radio"]:checked+.shipping-method-label {
  border-color: #ea9c00;
  background: #fef7e6;
}

.method-info .method-name {
  font-weight: 600;
  color: #1d2939;
  margin-bottom: 4px;
}

.method-details {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #667085;
}

.method-cost {
  font-weight: 700;
  color: #ea9c00;
  font-size: 1.1rem;
}

/* Vendor Subtotal */
.vendor-subtotal {
  padding: 20px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.vendor-subtotal .subtotal-row,
.vendor-subtotal .shipping-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  color: #667085;
}

.vendor-subtotal .vendor-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-top: 2px solid #ea9c00;
  margin-top: 8px;
  font-weight: 700;
  color: #1d2939;
}

.vendor-subtotal .total-amount {
  color: #ea9c00;
  font-size: 1.2rem;
}

/* Shipping Modal Styles */
.shipping-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shipping-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.shipping-modal-content {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.shipping-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.shipping-modal-header .modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1d2939;
}

.shipping-modal-header .modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  color: #667085;
  transition: all 0.3s ease;
}

.shipping-modal-header .modal-close:hover {
  background: #e9ecef;
  color: #1d2939;
}

.shipping-modal-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.vendor-name-display {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 500;
  color: #1d2939;
}

.shipping-methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modal-shipping-method {
  display: flex;
  align-items: center;
}

.modal-shipping-method input[type="radio"] {
  margin-right: 12px;
  transform: scale(1.1);
}

.modal-shipping-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 20px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-shipping-label:hover {
  border-color: #ea9c00;
  background: #fef7e6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(234, 156, 0, 0.15);
}

.modal-shipping-label.active {
  border-color: #ea9c00;
  background: #fef7e6;
  box-shadow: 0 0 0 1px rgba(234, 156, 0, 0.2);
}

.modal-shipping-label .method-info .method-name {
  font-weight: 600;
  color: #1d2939;
  margin-bottom: 6px;
  font-size: 1rem;
}

.modal-shipping-label .method-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.9rem;
  color: #667085;
}

.modal-shipping-label .method-cost {
  font-weight: 700;
  color: #ea9c00;
  font-size: 1.1rem;
}

.shipping-modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.shipping-modal-footer .btn-cancel,
.shipping-modal-footer .btn-confirm {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.95rem;
}

.shipping-modal-footer .btn-cancel {
  background: #6b7280;
  color: white;
}

.shipping-modal-footer .btn-cancel:hover {
  background: #4b5563;
}

.shipping-modal-footer .btn-confirm {
  background: #ea9c00;
  color: white;
}

.shipping-modal-footer .btn-confirm:hover {
  background: #d18a00;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(234, 156, 0, 0.3);
}

/* Enhanced Order Summary Styles */
.cart-simple-totals .total-row .total-label i {
  width: 16px;
  height: 16px;
  color: #ea9c00;
  margin-right: 8px;
}

.cart-simple-totals .total-row.shipping-breakdown .total-label small {
  font-weight: 400;
  font-size: 0.8rem;
  color: #667085;
  margin-left: 4px;
}

/* Feather Icons Styling */
.feather-sm {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* Cart Header Enhancements */
.cart-header {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px 20px !important;
  margin-bottom: 20px !important;
  border: 1px solid #e9ecef;
}

.cart-heder-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1d2939;
}

/* Empty Cart State */
.woocommerce-cart-form:empty::before {
  content: "Your cart is empty";
  display: block;
  text-align: center;
  padding: 60px 20px;
  color: #667085;
  font-size: 1.1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* Loading States */
.vendor-cart-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

.vendor-cart-section.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #ea9c00;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Accessibility Improvements */
.vendor-select-all:focus,
.cart-item-checkbox:focus,
input[name^="vendor_shipping"]:focus {
  outline: 2px solid #ea9c00;
  outline-offset: 2px;
}

.shipping-method-label:focus-within {
  outline: 2px solid #ea9c00;
  outline-offset: 2px;
}

/* Print Styles */
@media print {

  .vendor-actions,
  .cart-header,
  .vendor-shipping-section,
  .checkout-button-wrapper {
    display: none !important;
  }

  .vendor-cart-section {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {

  /* Shipping Selection Button Mobile */
  .shipping-selection-btn {
    max-width: 100%;
    font-size: 0.85rem;
    padding: 10px 12px;
  }

  /* Shipping Modal Mobile */
  .shipping-modal-content {
    width: 95%;
    max-height: 90vh;
    border-radius: 12px;
  }

  .shipping-modal-header {
    padding: 16px 20px;
  }

  .shipping-modal-header .modal-title {
    font-size: 1.1rem;
  }

  .shipping-modal-body {
    padding: 20px;
    max-height: 60vh;
  }

  .modal-shipping-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 14px 16px;
  }

  .modal-shipping-label .method-cost {
    align-self: flex-end;
    margin-top: -8px;
  }

  .shipping-modal-footer {
    padding: 16px 20px;
    flex-direction: column;
    gap: 10px;
  }

  .shipping-modal-footer .btn-cancel,
  .shipping-modal-footer .btn-confirm {
    width: 100%;
    padding: 14px 20px;
  }

  .vendor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .vendor-info {
    width: 100%;
  }

  .vendor-actions {
    width: 100%;
  }

  .vendor-actions .vendor-delete-all {
    width: 100%;
    justify-content: center;
  }

  .cart-product-item {
    flex-direction: column;
    padding: 15px;
  }

  .cart-product-item .product-select {
    position: absolute;
    top: 15px;
    right: 15px;
    margin: 0;
  }

  .cart-product-item .product-info {
    margin-top: 35px;
    width: 100%;
  }

  .cart-product-item .product-thumbnail {
    width: 80px;
    height: 80px;
  }

  .cart-product-item .product-subtotal {
    position: absolute;
    top: 15px;
    left: 15px;
    font-size: 1.1rem;
    background: rgba(234, 156, 0, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
  }

  .cart-product-item .quantity-control {
    justify-content: center;
    margin-top: 15px;
    padding-top: 15px;
  }

  .vendor-shipping-section {
    padding: 15px;
  }

  .shipping-methods {
    gap: 10px;
  }

  .shipping-method-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
  }

  .method-details {
    flex-direction: column;
    gap: 4px;
  }

  .method-cost {
    align-self: flex-end;
    margin-top: -20px;
  }

  .vendor-subtotal {
    padding: 15px;
  }
}

@media screen and (max-width: 480px) {
  .vendor-cart-section {
    margin-bottom: 15px;
    border-radius: 8px;
  }

  .vendor-header {
    padding: 12px;
  }

  .vendor-details .vendor-name {
    font-size: 1rem;
  }

  .cart-product-item {
    padding: 16px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .cart-product-item .product-select {
    position: absolute;
    top: 12px;
    right: 12px;
    margin: 0;
  }

  .cart-product-item .product-info {
    width: 100%;
    margin-top: 20px;
  }

  .cart-product-item .product-thumbnail {
    width: 60px;
    height: 60px;
  }

  .cart-product-item .product-name {
    font-size: 0.95rem;
    white-space: normal;
    max-width: none;
  }

  .cart-product-item .product-price-section {
    position: absolute;
    top: 12px;
    left: 12px;
    margin: 0;
    align-items: flex-start;
  }

  .cart-product-item .product-subtotal {
    font-size: 1.1rem;
    background: rgba(234, 156, 0, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
  }

  .cart-product-item .product-actions {
    width: 100%;
    justify-content: center;
    margin: 0;
    margin-top: 8px;
  }

  .cart-product-item .remove-item {
    width: 100%;
    justify-content: center;
  }

  .cart-product-item .quantity-control {
    justify-content: center;
    margin-top: 8px;
  }

  .cart-product-item .quantity-control button {
    width: 28px;
    height: 28px;
  }

  .cart-product-item .quantity-control .qty-input {
    width: 45px;
    height: 28px;
    font-size: 13px;
  }

  .vendor-shipping-section {
    padding: 12px;
  }

  .shipping-method-label {
    padding: 10px;
  }

  .vendor-subtotal {
    padding: 12px;
  }
}