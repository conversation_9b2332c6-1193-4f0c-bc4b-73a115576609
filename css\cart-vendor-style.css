/* AliExpress-style Cart with Vendor Grouping */

/* Override default WooCommerce cart table styles */
.woocommerce-cart-form .shop_table.cart {
  display: none !important;
}

.woocommerce-cart-form .shop_table.cart thead,
.woocommerce-cart-form .shop_table.cart tbody {
  display: none !important;
}

/* Vendor Cart Section */
.vendor-cart-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  display: block !important;
}

.vendor-cart-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Vendor Header */
.vendor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.vendor-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.vendor-select-all {
  transform: scale(1.2);
  margin: 0;
}

.vendor-details .vendor-name {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d2939;
  display: flex;
  align-items: center;
  gap: 8px;
}

.vendor-details .vendor-name a {
  color: #1d2939;
  text-decoration: none;
  transition: color 0.3s ease;
}

.vendor-details .vendor-name a:hover {
  color: #ea9c00;
}

.vendor-details .vendor-item-count {
  font-size: 0.9rem;
  color: #667085;
}

.vendor-actions .vendor-delete-all {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #f04438;
  border-radius: 8px;
  color: #f04438;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.vendor-actions .vendor-delete-all:hover {
  background: #f04438;
  color: white;
}

/* Vendor Products */
.vendor-products {
  padding: 0;
}

.cart-product-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  transition: background-color 0.3s ease;
}

.cart-product-item:last-child {
  border-bottom: none;
}

.cart-product-item:hover {
  background-color: #fafafa;
}

.cart-product-item .product-select {
  margin-right: 15px;
  margin-top: 5px;
}

.cart-product-item .product-select input[type="checkbox"] {
  transform: scale(1.2);
  margin: 0;
}

.cart-product-item .product-info {
  flex: 1;
  display: flex !important;
  gap: 15px;
  align-items: flex-start;
}

.cart-product-item .product-thumbnail {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.cart-product-item .product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-product-item .product-details {
  flex: 1;
  min-width: 0;
}

.cart-product-item .product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d2939;
  text-decoration: none;
  line-height: 1.4;
  margin-bottom: 8px;
  display: block;
}

.cart-product-item .product-name:hover {
  color: #ea9c00;
}

.cart-product-item .product-reviews {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.cart-product-item .quantity-control {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.cart-product-item .quantity-control button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: #ea9c00;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-product-item .quantity-control button:hover {
  background: #d18a00;
  transform: scale(1.05);
}

.cart-product-item .quantity-control .qty-input {
  width: 60px;
  height: 36px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-weight: 600;
}

.cart-product-item .quantity-control .remove {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  color: #f04438;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-left: 10px;
}

.cart-product-item .quantity-control .remove:hover {
  background: #f04438;
  color: white;
}

.cart-product-item .product-subtotal {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 1.2rem;
  font-weight: 700;
  color: #ea9c00;
}

/* Vendor Shipping Section */
.vendor-shipping-section {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.vendor-shipping-section .shipping-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 15px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1d2939;
}

.shipping-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shipping-method-option {
  display: flex;
  align-items: center;
}

.shipping-method-option input[type="radio"] {
  margin-right: 12px;
  transform: scale(1.1);
}

.shipping-method-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shipping-method-label:hover {
  border-color: #ea9c00;
  background: #fef7e6;
}

.shipping-method-option input[type="radio"]:checked+.shipping-method-label {
  border-color: #ea9c00;
  background: #fef7e6;
}

.method-info .method-name {
  font-weight: 600;
  color: #1d2939;
  margin-bottom: 4px;
}

.method-details {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #667085;
}

.method-cost {
  font-weight: 700;
  color: #ea9c00;
  font-size: 1.1rem;
}

/* Vendor Subtotal */
.vendor-subtotal {
  padding: 20px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.vendor-subtotal .subtotal-row,
.vendor-subtotal .shipping-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  color: #667085;
}

.vendor-subtotal .vendor-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-top: 2px solid #ea9c00;
  margin-top: 8px;
  font-weight: 700;
  color: #1d2939;
}

.vendor-subtotal .total-amount {
  color: #ea9c00;
  font-size: 1.2rem;
}

/* Enhanced Order Summary Styles */
.cart-simple-totals .total-row .total-label i {
  width: 16px;
  height: 16px;
  color: #ea9c00;
  margin-right: 8px;
}

.cart-simple-totals .total-row.shipping-breakdown .total-label small {
  font-weight: 400;
  font-size: 0.8rem;
  color: #667085;
  margin-left: 4px;
}

/* Feather Icons Styling */
.feather-sm {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* Cart Header Enhancements */
.cart-header {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px 20px !important;
  margin-bottom: 20px !important;
  border: 1px solid #e9ecef;
}

.cart-heder-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1d2939;
}

/* Empty Cart State */
.woocommerce-cart-form:empty::before {
  content: "Your cart is empty";
  display: block;
  text-align: center;
  padding: 60px 20px;
  color: #667085;
  font-size: 1.1rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* Loading States */
.vendor-cart-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

.vendor-cart-section.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #ea9c00;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Accessibility Improvements */
.vendor-select-all:focus,
.cart-item-checkbox:focus,
input[name^="vendor_shipping"]:focus {
  outline: 2px solid #ea9c00;
  outline-offset: 2px;
}

.shipping-method-label:focus-within {
  outline: 2px solid #ea9c00;
  outline-offset: 2px;
}

/* Print Styles */
@media print {

  .vendor-actions,
  .cart-header,
  .vendor-shipping-section,
  .checkout-button-wrapper {
    display: none !important;
  }

  .vendor-cart-section {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
  .vendor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .vendor-info {
    width: 100%;
  }

  .vendor-actions {
    width: 100%;
  }

  .vendor-actions .vendor-delete-all {
    width: 100%;
    justify-content: center;
  }

  .cart-product-item {
    flex-direction: column;
    padding: 15px;
  }

  .cart-product-item .product-select {
    position: absolute;
    top: 15px;
    right: 15px;
    margin: 0;
  }

  .cart-product-item .product-info {
    margin-top: 35px;
    width: 100%;
  }

  .cart-product-item .product-thumbnail {
    width: 80px;
    height: 80px;
  }

  .cart-product-item .product-subtotal {
    position: absolute;
    top: 15px;
    left: 15px;
    font-size: 1.1rem;
    background: rgba(234, 156, 0, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
  }

  .cart-product-item .quantity-control {
    justify-content: center;
    margin-top: 15px;
    padding-top: 15px;
  }

  .vendor-shipping-section {
    padding: 15px;
  }

  .shipping-methods {
    gap: 10px;
  }

  .shipping-method-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
  }

  .method-details {
    flex-direction: column;
    gap: 4px;
  }

  .method-cost {
    align-self: flex-end;
    margin-top: -20px;
  }

  .vendor-subtotal {
    padding: 15px;
  }
}

@media screen and (max-width: 480px) {
  .vendor-cart-section {
    margin-bottom: 15px;
    border-radius: 8px;
  }

  .vendor-header {
    padding: 12px;
  }

  .vendor-details .vendor-name {
    font-size: 1rem;
  }

  .cart-product-item {
    padding: 12px;
  }

  .cart-product-item .product-thumbnail {
    width: 70px;
    height: 70px;
  }

  .cart-product-item .product-name {
    font-size: 1rem;
  }

  .cart-product-item .quantity-control button {
    width: 32px;
    height: 32px;
  }

  .cart-product-item .quantity-control .qty-input {
    width: 50px;
    height: 32px;
  }

  .vendor-shipping-section {
    padding: 12px;
  }

  .shipping-method-label {
    padding: 10px;
  }

  .vendor-subtotal {
    padding: 12px;
  }
}