<?php
/**
 * Cart Page - AliExpress Style with Vendor Grouping
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.9.0
 */

defined( 'ABSPATH' ) || exit;

do_action( 'woocommerce_before_cart' ); ?>
<div class="cart-container container">
  <form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post">
    <input type="hidden" name="update_cart" value="1">
    <?php wp_nonce_field('woocommerce-cart', 'woocommerce-cart-nonce'); ?>

    <div class="cart-header mt-4 pt-4 mb-2 row d-flex">
      <div class="cart-heder-checkbox col-6 text-start">
        <input type="checkbox" id="select-all"> Select All
      </div>
      <div class="cart-header-delbtn col-6 text-end">
        <button type="button" id="delete-selected-cart-items" class="button btn-del-selct">
          <i data-feather="trash-2" class="feather-sm"></i>
          Delete selected items
        </button>
      </div>
    </div>
    <?php do_action( 'woocommerce_before_cart_table' ); ?>

    <?php
    // Group cart items by vendor
    $cart_items_by_vendor = array();
    $vendor_info = array();

    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
        $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

        if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
            // Get vendor ID
            $vendor_id = 0;
            $vendor_name = __('Direct Sale', 'tendeal');
            $vendor_url = '';

            if ( function_exists( 'wcfmmp_get_store_url' ) ) {
                $vendor_id = get_post_field('post_author', $product_id);
                if ( $vendor_id ) {
                    $vendor_name = wcfm_get_vendor_store_name($vendor_id);
                    $vendor_url = wcfmmp_get_store_url($vendor_id);
                }
            }

            // Group items by vendor
            if ( !isset($cart_items_by_vendor[$vendor_id]) ) {
                $cart_items_by_vendor[$vendor_id] = array();
                $vendor_info[$vendor_id] = array(
                    'name' => $vendor_name,
                    'url' => $vendor_url,
                    'id' => $vendor_id
                );
            }

            $cart_items_by_vendor[$vendor_id][$cart_item_key] = $cart_item;
        }
    }

    do_action( 'woocommerce_before_cart_contents' );

    // Display cart items grouped by vendor
    foreach ( $cart_items_by_vendor as $vendor_id => $vendor_cart_items ) :
        $vendor = $vendor_info[$vendor_id];
        $vendor_subtotal = 0;

        // Calculate vendor subtotal
        foreach ( $vendor_cart_items as $cart_item_key => $cart_item ) {
            $_product = $cart_item['data'];
            $vendor_subtotal += $_product->get_price() * $cart_item['quantity'];
        }
        ?>

        <!-- Vendor Section -->
        <div class="vendor-cart-section" data-vendor-id="<?php echo esc_attr($vendor_id); ?>">
          <div class="vendor-header">
            <div class="vendor-info">
              <input type="checkbox" class="vendor-select-all" data-vendor="<?php echo esc_attr($vendor_id); ?>">
              <div class="vendor-details">
                <h3 class="vendor-name">
                  <i data-feather="store" class="feather-sm"></i>
                  <?php if ( $vendor['url'] ) : ?>
                    <a href="<?php echo esc_url($vendor['url']); ?>"><?php echo esc_html($vendor['name']); ?></a>
                  <?php else : ?>
                    <?php echo esc_html($vendor['name']); ?>
                  <?php endif; ?>
                </h3>
                <span class="vendor-item-count"><?php echo count($vendor_cart_items); ?> <?php _e('items', 'tendeal'); ?></span>
              </div>
            </div>
            <div class="vendor-actions">
              <button type="button" class="vendor-delete-all" data-vendor="<?php echo esc_attr($vendor_id); ?>">
                <i data-feather="trash-2" class="feather-sm"></i>
                <?php _e('Delete All', 'tendeal'); ?>
              </button>
            </div>
          </div>
          <!-- Vendor Products -->
          <div class="vendor-products">
            <?php foreach ( $vendor_cart_items as $cart_item_key => $cart_item ) :
                $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
                $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );
                $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
                ?>

                <div class="cart-product-item <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>" data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>">
                  <div class="product-select">
                    <input type="checkbox" class="cart-item-checkbox" name="cart_item_keys[]" value="<?php echo esc_attr( $cart_item_key ); ?>" data-vendor="<?php echo esc_attr($vendor_id); ?>">
                  </div>

                  <div class="product-info d-flex">
                    <div class="product-thumbnail">
                      <a href="<?php echo esc_url( $product_permalink ); ?>">
                        <?php echo $_product->get_image(); ?>
                      </a>
                    </div>
                    <div class="product-details">
                      <a href="<?php echo esc_url( $product_permalink ); ?>" class="product-name"><?php echo $_product->get_name(); ?></a>

                      <?php
                      // Display product meta data
                      echo wc_get_formatted_cart_item_data( $cart_item ); // PHPCS: XSS ok.

                      // Add Product Reviews
                      $rating_count = $_product->get_rating_count();
                      $review_count = $_product->get_review_count();
                      $average_rating = $_product->get_average_rating();

                      if ( $review_count > 0 ) {
                          echo '<div class="product-reviews d-flex mt-2 mb-2">';
                          echo '<div class="star-rating" title="' . sprintf( esc_html__( 'Rated %s out of 5', 'woocommerce' ), $average_rating ) . '">';
                          echo '<span style="width:' . ( ( $average_rating / 5 ) * 100 ) . '%">
                                      <strong class="rating">' . esc_html( $average_rating ) . '</strong>
                                  </span>';
                          echo '</div>';
                          echo '<strong class="rating">' . esc_html( $average_rating ) . '</strong>';
                          echo '<a href="' . esc_url( get_permalink( $product_id ) ) . '#reviews" class="woocommerce-review-link" rel="nofollow">' . sprintf( _n( '%s customer review', '%s Reviews', $review_count, 'woocommerce' ), '<span class="count">' . esc_html( $review_count ) . '</span>' ) . '</a>';
                          echo '</div>';
                      }
                      ?>

                      <div class="quantity-control mt-2 mb-2">
                        <button type="button" class="decrease-qty"><i data-feather="minus" class="feather-sm"></i></button>
                        <input type="number" name="cart[<?php echo $cart_item_key; ?>][qty]" value="<?php echo $cart_item['quantity']; ?>" min="1" class="qty-input">
                        <button type="button" class="increase-qty"><i data-feather="plus" class="feather-sm"></i></button>
                        <div class="d-flex delete-btn">
                          <a href="<?php echo esc_url( wc_get_cart_remove_url( $cart_item_key ) ); ?>" class="remove">
                            <i data-feather="trash-2" class="feather-sm"></i> Delete Item
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="product-subtotal" data-title="<?php esc_attr_e( 'Subtotal', 'woocommerce' ); ?>">
                    <?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?>
                  </div>
                </div>
            <?php endforeach; ?>
          </div>


          <!-- Vendor Shipping Options -->
          <div class="vendor-shipping-section">
            <h4 class="shipping-title">
              <i data-feather="truck" class="feather-sm"></i>
              <?php _e('Shipping Options', 'tendeal'); ?>
            </h4>

            <div class="shipping-methods">
              <?php
              // Get available shipping methods for this vendor
              $shipping_methods = array(
                'standard' => array(
                  'name' => __('Standard Shipping', 'tendeal'),
                  'cost' => 15.00,
                  'time' => __('7-15 days', 'tendeal'),
                  'description' => __('Regular delivery service', 'tendeal')
                ),
                'express' => array(
                  'name' => __('Express Shipping', 'tendeal'),
                  'cost' => 25.00,
                  'time' => __('3-7 days', 'tendeal'),
                  'description' => __('Faster delivery service', 'tendeal')
                ),
                'premium' => array(
                  'name' => __('Premium Shipping', 'tendeal'),
                  'cost' => 35.00,
                  'time' => __('1-3 days', 'tendeal'),
                  'description' => __('Fastest delivery service', 'tendeal')
                )
              );

              foreach ( $shipping_methods as $method_id => $method ) :
              ?>
                <div class="shipping-method-option">
                  <input type="radio"
                         id="shipping_<?php echo esc_attr($vendor_id); ?>_<?php echo esc_attr($method_id); ?>"
                         name="vendor_shipping[<?php echo esc_attr($vendor_id); ?>]"
                         value="<?php echo esc_attr($method_id); ?>"
                         data-cost="<?php echo esc_attr($method['cost']); ?>"
                         <?php echo ($method_id === 'standard') ? 'checked' : ''; ?>>
                  <label for="shipping_<?php echo esc_attr($vendor_id); ?>_<?php echo esc_attr($method_id); ?>" class="shipping-method-label">
                    <div class="method-info">
                      <div class="method-name"><?php echo esc_html($method['name']); ?></div>
                      <div class="method-details">
                        <span class="method-time"><?php echo esc_html($method['time']); ?></span>
                        <span class="method-description"><?php echo esc_html($method['description']); ?></span>
                      </div>
                    </div>
                    <div class="method-cost">
                      <?php echo wc_price($method['cost']); ?>
                    </div>
                  </label>
                </div>
              <?php endforeach; ?>
            </div>
          </div>

          <!-- Vendor Subtotal -->
          <div class="vendor-subtotal">
            <div class="subtotal-row">
              <span class="subtotal-label"><?php _e('Subtotal', 'tendeal'); ?>:</span>
              <span class="subtotal-amount"><?php echo wc_price($vendor_subtotal); ?></span>
            </div>
            <div class="shipping-row">
              <span class="shipping-label"><?php _e('Shipping', 'tendeal'); ?>:</span>
              <span class="shipping-amount" data-vendor="<?php echo esc_attr($vendor_id); ?>"><?php echo wc_price(15.00); ?></span>
            </div>
            <div class="vendor-total-row">
              <span class="total-label"><?php _e('Total', 'tendeal'); ?>:</span>
              <span class="total-amount" data-vendor="<?php echo esc_attr($vendor_id); ?>"><?php echo wc_price($vendor_subtotal + 15.00); ?></span>
            </div>
          </div>
        </div>

    <?php endforeach; ?>

    <?php do_action( 'woocommerce_cart_contents' ); ?>
    <?php do_action( 'woocommerce_after_cart_contents' ); ?>
    <?php do_action( 'woocommerce_after_cart_table' ); ?>
  </form>

  <aside class="cart-sidebar mt-4">
    <!-- Address Box -->
    <div class="cart-address-box">
      <h4><?php esc_html_e( 'Delivery Address', 'tendeal' ); ?></h4>
      <?php
      // Get customer address information
      $customer = WC()->customer;
      $has_shipping_address = false;
      $address_display = '';

      if ( is_user_logged_in() ) {
        // Check if customer has shipping address
        $shipping_address = array(
          'first_name' => $customer->get_shipping_first_name(),
          'last_name'  => $customer->get_shipping_last_name(),
          'company'    => $customer->get_shipping_company(),
          'address_1'  => $customer->get_shipping_address_1(),
          'address_2'  => $customer->get_shipping_address_2(),
          'city'       => $customer->get_shipping_city(),
          'state'      => $customer->get_shipping_state(),
          'postcode'   => $customer->get_shipping_postcode(),
          'country'    => $customer->get_shipping_country(),
        );

        // If no shipping address, fall back to billing
        if ( empty( array_filter( $shipping_address ) ) ) {
          $shipping_address = array(
            'first_name' => $customer->get_billing_first_name(),
            'last_name'  => $customer->get_billing_last_name(),
            'company'    => $customer->get_billing_company(),
            'address_1'  => $customer->get_billing_address_1(),
            'address_2'  => $customer->get_billing_address_2(),
            'city'       => $customer->get_billing_city(),
            'state'      => $customer->get_billing_state(),
            'postcode'   => $customer->get_billing_postcode(),
            'country'    => $customer->get_billing_country(),
          );
        }

        // Check if we have any address data
        $has_shipping_address = !empty( array_filter( $shipping_address ) );

        if ( $has_shipping_address ) {
          $formatted_address = WC()->countries->get_formatted_address( $shipping_address );
          $address_display = $formatted_address;
        }
      }
      ?>

      <div class="address-content">
        <?php if ( $has_shipping_address && !empty( $address_display ) ) : ?>
          <div class="current-address">
            <div class="address-icon">
              <i data-feather="map-pin"></i>
            </div>
            <div class="address-text">
              <?php echo wp_kses_post( $address_display ); ?>
            </div>
          </div>
        <?php else : ?>
          <div class="no-address">
            <div class="address-icon">
              <i data-feather="map-pin"></i>
            </div>
            <div class="address-text">
              <p><?php esc_html_e( 'No delivery address set', 'tendeal' ); ?></p>
            </div>
          </div>
        <?php endif; ?>

        <div class="address-actions">
          <?php if ( is_user_logged_in() ) : ?>
            <button type="button" class="btn-update-address" id="toggle-address-form">
              <i data-feather="edit-2"></i>
              <?php esc_html_e( 'Update Address', 'tendeal' ); ?>
            </button>
          <?php else : ?>
            <a href="<?php echo esc_url( wc_get_page_permalink( 'myaccount' ) ); ?>" class="btn-update-address">
              <i data-feather="user"></i>
              <?php esc_html_e( 'Login to set address', 'tendeal' ); ?>
            </a>
          <?php endif; ?>
        </div>

        <!-- Address Update Form (Hidden by default) -->
        <?php if ( is_user_logged_in() ) : ?>
          <div class="address-update-form" id="address-update-form" style="display: none;">
            <form id="cart-address-form">
              <?php wp_nonce_field( 'update_cart_address', 'cart_address_nonce' ); ?>

              <div class="form-row">
                <div class="form-group half">
                  <label for="shipping_first_name"><?php esc_html_e( 'First Name', 'tendeal' ); ?> *</label>
                  <input type="text" id="shipping_first_name" name="shipping_first_name"
                         value="<?php echo esc_attr( $customer->get_shipping_first_name() ?: $customer->get_billing_first_name() ); ?>" required>
                </div>
                <div class="form-group half">
                  <label for="shipping_last_name"><?php esc_html_e( 'Last Name', 'tendeal' ); ?> *</label>
                  <input type="text" id="shipping_last_name" name="shipping_last_name"
                         value="<?php echo esc_attr( $customer->get_shipping_last_name() ?: $customer->get_billing_last_name() ); ?>" required>
                </div>
              </div>

              <div class="form-group">
                <label for="shipping_address_1"><?php esc_html_e( 'Address Line 1', 'tendeal' ); ?> *</label>
                <input type="text" id="shipping_address_1" name="shipping_address_1"
                       value="<?php echo esc_attr( $customer->get_shipping_address_1() ?: $customer->get_billing_address_1() ); ?>" required>
              </div>

              <div class="form-group">
                <label for="shipping_address_2"><?php esc_html_e( 'Address Line 2', 'tendeal' ); ?></label>
                <input type="text" id="shipping_address_2" name="shipping_address_2"
                       value="<?php echo esc_attr( $customer->get_shipping_address_2() ?: $customer->get_billing_address_2() ); ?>">
              </div>

              <div class="form-row">
                <div class="form-group half">
                  <label for="shipping_city"><?php esc_html_e( 'City', 'tendeal' ); ?> *</label>
                  <input type="text" id="shipping_city" name="shipping_city"
                         value="<?php echo esc_attr( $customer->get_shipping_city() ?: $customer->get_billing_city() ); ?>" required>
                </div>
                <div class="form-group half">
                  <label for="shipping_postcode"><?php esc_html_e( 'Postal Code', 'tendeal' ); ?></label>
                  <input type="text" id="shipping_postcode" name="shipping_postcode"
                         value="<?php echo esc_attr( $customer->get_shipping_postcode() ?: $customer->get_billing_postcode() ); ?>">
                </div>
              </div>

              <div class="form-row">
                <div class="form-group half">
                  <label for="shipping_state"><?php esc_html_e( 'State/Province', 'tendeal' ); ?></label>
                  <input type="text" id="shipping_state" name="shipping_state"
                         value="<?php echo esc_attr( $customer->get_shipping_state() ?: $customer->get_billing_state() ); ?>">
                </div>
                <div class="form-group half">
                  <label for="shipping_country"><?php esc_html_e( 'Country', 'tendeal' ); ?> *</label>
                  <select id="shipping_country" name="shipping_country" required>
                    <?php
                    $countries = WC()->countries->get_countries();
                    $selected_country = $customer->get_shipping_country() ?: $customer->get_billing_country();
                    foreach ( $countries as $code => $name ) {
                      echo '<option value="' . esc_attr( $code ) . '"' . selected( $selected_country, $code, false ) . '>' . esc_html( $name ) . '</option>';
                    }
                    ?>
                  </select>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn-cancel" id="cancel-address-form">
                  <i data-feather="x"></i>
                  <?php esc_html_e( 'Cancel', 'tendeal' ); ?>
                </button>
                <button type="submit" class="btn-save">
                  <i data-feather="check"></i>
                  <?php esc_html_e( 'Save Address', 'tendeal' ); ?>
                </button>
              </div>
            </form>
          </div>
        <?php endif; ?>
      </div>
    </div>

    <!-- Coupon Box -->
    <div class="cart-coupon">
      <span>Coupon Code</span>
      <input type="text" id="coupon_code" placeholder="Enter coupon code">
      <button type="button" id="apply_coupon" class="button">Apply Coupon</button>
      <p id="coupon_message" class="coupon-message"></p>
    </div>

    <!-- Enhanced Cart Totals with Vendor Breakdown -->
    <div class="cart-simple-totals">
      <h4><?php esc_html_e( 'Order Summary', 'tendeal' ); ?></h4>
      <div class="totals-content">
        <?php
        // Get cart totals
        $cart = WC()->cart;
        $subtotal = $cart->get_subtotal();
        $discount_total = $cart->get_discount_total();
        $applied_coupons = $cart->get_applied_coupons();

        // Calculate total shipping from all vendors (default to standard shipping)
        $total_vendor_shipping = count($cart_items_by_vendor) * 15.00; // Default standard shipping per vendor

        // Calculate final total
        $final_total = $subtotal + $total_vendor_shipping - $discount_total;
        ?>

        <!-- Subtotal -->
        <div class="total-row subtotal">
          <span class="total-label">
            <i data-feather="shopping-cart" class="feather-sm"></i>
            <?php esc_html_e( 'Subtotal', 'tendeal' ); ?>
          </span>
          <span class="total-value">
            <?php echo wp_kses_post( wc_price( $subtotal ) ); ?>
          </span>
        </div>

        <!-- Vendor Shipping Breakdown -->
        <div class="total-row shipping-breakdown">
          <span class="total-label">
            <i data-feather="truck" class="feather-sm"></i>
            <?php esc_html_e( 'Shipping', 'tendeal' ); ?>
            <small>(<?php echo count($cart_items_by_vendor); ?> <?php _e('vendors', 'tendeal'); ?>)</small>
          </span>
          <span class="total-value" id="total-shipping-amount">
            <?php echo wp_kses_post( wc_price( $total_vendor_shipping ) ); ?>
          </span>
        </div>

        <!-- Discount (only show if coupons are applied) -->
        <?php if ( !empty( $applied_coupons ) && $discount_total > 0 ) : ?>
          <div class="total-row discount-total">
            <span class="total-label">
              <i data-feather="tag" class="feather-sm"></i>
              <?php esc_html_e( 'Discount', 'tendeal' ); ?>
              <?php if ( count( $applied_coupons ) === 1 ) : ?>
                <small>(<?php echo esc_html( $applied_coupons[0] ); ?>)</small>
              <?php endif; ?>
            </span>
            <span class="total-value discount-value">
              -<?php echo wp_kses_post( wc_price( $discount_total ) ); ?>
            </span>
          </div>
        <?php endif; ?>

        <!-- Total -->
        <div class="total-row order-total">
          <span class="total-label">
            <i data-feather="credit-card" class="feather-sm"></i>
            <?php esc_html_e( 'Total', 'tendeal' ); ?>
          </span>
          <span class="total-value" id="final-total-amount">
            <?php echo wp_kses_post( wc_price( $final_total ) ); ?>
          </span>
        </div>

        <div class="checkout-button-wrapper">
          <a href="<?php echo esc_url( wc_get_checkout_url() ); ?>" class="btn-checkout">
            <i data-feather="credit-card"></i>
            <?php esc_html_e( 'Proceed to Checkout', 'tendeal' ); ?>
          </a>
        </div>
      </div>
    </div>


    <div class="payment-methods">
      <h2><?php esc_html_e( 'Payment Methods', 'woocommerce' ); ?></h2>
      <p><?php esc_html_e( 'Accepted payment methods will be displayed during checkout.', 'woocommerce' ); ?></p>
      <?php
    // In a real scenario, you might display payment gateway icons or information here.
    // This is highly theme-dependent and often handled in the checkout process.
    // For example, you could retrieve available payment gateways:
    /*
    $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
    if ( ! empty( $available_gateways ) ) {
        echo '<ul>';
        foreach ( $available_gateways as $gateway ) {
            echo '<li>' . esc_html( $gateway->title ) . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>' . esc_html__( 'No payment methods available.', 'woocommerce' ) . '</p>';
    }
    */
    ?>
    </div>


  </aside>
</div>




<?php do_action( 'woocommerce_before_cart_collaterals' ); ?>



<?php do_action( 'woocommerce_after_cart' ); ?>



<script>
// Select All functionality
document.getElementById('select-all').addEventListener('change', function() {
  let checkboxes = document.querySelectorAll('.cart-item-checkbox');
  let vendorCheckboxes = document.querySelectorAll('.vendor-select-all');

  checkboxes.forEach(checkbox => checkbox.checked = this.checked);
  vendorCheckboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// Vendor Select All functionality
document.querySelectorAll('.vendor-select-all').forEach(vendorCheckbox => {
  vendorCheckbox.addEventListener('change', function() {
    const vendorId = this.dataset.vendor;
    const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);

    vendorItems.forEach(checkbox => checkbox.checked = this.checked);

    // Update main select all checkbox
    updateMainSelectAll();
  });
});

// Individual item checkbox functionality
document.querySelectorAll('.cart-item-checkbox').forEach(itemCheckbox => {
  itemCheckbox.addEventListener('change', function() {
    const vendorId = this.dataset.vendor;
    updateVendorSelectAll(vendorId);
    updateMainSelectAll();
  });
});

// Update vendor select all checkbox based on individual items
function updateVendorSelectAll(vendorId) {
  const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);
  const vendorCheckbox = document.querySelector(`.vendor-select-all[data-vendor="${vendorId}"]`);

  if (vendorCheckbox) {
    const checkedItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]:checked`);
    vendorCheckbox.checked = checkedItems.length === vendorItems.length;
  }
}

// Update main select all checkbox
function updateMainSelectAll() {
  const allItems = document.querySelectorAll('.cart-item-checkbox');
  const checkedItems = document.querySelectorAll('.cart-item-checkbox:checked');
  const mainSelectAll = document.getElementById('select-all');

  if (mainSelectAll) {
    mainSelectAll.checked = checkedItems.length === allItems.length;
  }
}

// Quantity controls
document.querySelectorAll('.decrease-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.nextElementSibling;
    if (input.value > 1) {
      input.value--;
      updateCartQuantity(input);
    }
  });
});

document.querySelectorAll('.increase-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.previousElementSibling;
    input.value++;
    updateCartQuantity(input);
  });
});

function updateCartQuantity(input) {
  // Trigger change event to update cart
  const event = new Event('change', { bubbles: true });
  input.dispatchEvent(event);

  // Submit the form after a short delay
  setTimeout(function() {
    document.querySelector('.woocommerce-cart-form').submit();
  }, 300);
}


// Shipping method selection
document.querySelectorAll('input[name^="vendor_shipping"]').forEach(radio => {
  radio.addEventListener('change', function() {
    const vendorId = this.name.match(/\[(\d+)\]/)[1];
    const cost = parseFloat(this.dataset.cost);

    // Update shipping cost display
    const shippingAmount = document.querySelector(`.shipping-amount[data-vendor="${vendorId}"]`);
    const totalAmount = document.querySelector(`.total-amount[data-vendor="${vendorId}"]`);

    if (shippingAmount && totalAmount) {
      shippingAmount.textContent = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'QAR'
      }).format(cost);

      // Recalculate vendor total
      const subtotalElement = shippingAmount.closest('.vendor-subtotal').querySelector('.subtotal-amount');
      if (subtotalElement) {
        const subtotal = parseFloat(subtotalElement.textContent.replace(/[^\d.]/g, ''));
        const newTotal = subtotal + cost;
        totalAmount.textContent = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'QAR'
        }).format(newTotal);
      }
    }

    // Update overall cart totals
    updateCartTotals();
  });
});

// Vendor delete all functionality
document.querySelectorAll('.vendor-delete-all').forEach(button => {
  button.addEventListener('click', function() {
    const vendorId = this.dataset.vendor;
    const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);

    if (confirm('Are you sure you want to delete all items from this vendor?')) {
      // Check all items for this vendor
      vendorItems.forEach(checkbox => checkbox.checked = true);

      // Trigger delete selected items
      document.getElementById('delete-selected-cart-items').click();
    }
  });
});

// Update cart totals
function updateCartTotals() {
  // Calculate total shipping from all vendors
  let totalShipping = 0;
  const vendorSections = document.querySelectorAll('.vendor-cart-section');

  vendorSections.forEach(section => {
    const vendorId = section.dataset.vendorId;
    const selectedShipping = section.querySelector(`input[name="vendor_shipping[${vendorId}]"]:checked`);

    if (selectedShipping) {
      totalShipping += parseFloat(selectedShipping.dataset.cost);
    }
  });

  // Update total shipping display
  const totalShippingElement = document.getElementById('total-shipping-amount');
  if (totalShippingElement) {
    totalShippingElement.textContent = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'QAR'
    }).format(totalShipping);
  }

  // Calculate and update final total
  const subtotalElement = document.querySelector('.total-row.subtotal .total-value');
  const discountElement = document.querySelector('.total-row.discount-total .total-value');
  const finalTotalElement = document.getElementById('final-total-amount');

  if (subtotalElement && finalTotalElement) {
    let subtotal = parseFloat(subtotalElement.textContent.replace(/[^\d.]/g, ''));
    let discount = 0;

    if (discountElement) {
      discount = parseFloat(discountElement.textContent.replace(/[^\d.]/g, ''));
    }

    const finalTotal = subtotal + totalShipping - discount;

    finalTotalElement.textContent = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'QAR'
    }).format(finalTotal);
  }
}

// Initialize vendor cart functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize shipping method calculations
  updateCartTotals();

  // Initialize feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }
});

jQuery(document).ready(function($) {
  // Initialize Feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }

  // Address form toggle functionality
  $('#toggle-address-form').click(function() {
    var $form = $('#address-update-form');
    var $button = $(this);

    if ($form.is(':visible')) {
      $form.slideUp(300);
      $button.find('i').attr('data-feather', 'edit-2');
      $button.find('span').text('<?php esc_html_e( 'Update Address', 'tendeal' ); ?>');
    } else {
      $form.slideDown(300);
      $button.find('i').attr('data-feather', 'x');
      $button.find('span').text('<?php esc_html_e( 'Cancel', 'tendeal' ); ?>');
    }

    // Re-initialize feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }
  });

  // Cancel address form
  $('#cancel-address-form').click(function() {
    $('#address-update-form').slideUp(300);
    var $toggleButton = $('#toggle-address-form');
    $toggleButton.find('i').attr('data-feather', 'edit-2');
    $toggleButton.find('span').text('<?php esc_html_e( 'Update Address', 'tendeal' ); ?>');

    // Re-initialize feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }
  });

  // Handle address form submission
  $('#cart-address-form').submit(function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    formData += '&action=update_cart_address';

    // Show loading state
    var $saveButton = $('.btn-save');
    var originalText = $saveButton.html();
    $saveButton.html('<i data-feather="loader"></i> Saving...').prop('disabled', true);

    if (typeof feather !== 'undefined') {
      feather.replace();
    }

    $.ajax({
      url: wc_ajax_params.ajax_url,
      type: 'POST',
      data: formData,
      success: function(response) {
        if (response.success) {
          // Show success message
          alert('Address updated successfully!');
          // Reload the page to show updated address
          location.reload();
        } else {
          alert('Error: ' + (response.data.message || 'Failed to update address'));
          $saveButton.html(originalText).prop('disabled', false);
          if (typeof feather !== 'undefined') {
            feather.replace();
          }
        }
      },
      error: function() {
        alert('Error: Failed to update address. Please try again.');
        $saveButton.html(originalText).prop('disabled', false);
        if (typeof feather !== 'undefined') {
          feather.replace();
        }
      }
    });
  });

  // Fix for select-all checkbox
  $('#select-all').click(function() {
    $('.cart-item-checkbox').prop('checked', $(this).prop('checked'));
  });

  $('#delete-selected-cart-items').click(function() {
    var selectedItems = [];
    $('.cart-item-checkbox:checked').each(function() {
      selectedItems.push($(this).val());
    });

    if (selectedItems.length > 0) {
      // Use standard jQuery AJAX, and ensure data is correctly formatted
      $.ajax({
        type: 'POST',
        url: wc_ajax_params.ajax_url, // From WooCommerce
        data: {
          action: 'remove_selected_cart_items', // Your action name
          cart_item_keys: selectedItems
        },
        success: function(response) {
          window.location.reload();
        },
        error: function(jqXHR, textStatus, errorThrown) {
          console.log('Error deleting items:', textStatus, errorThrown);
          alert('Failed to delete selected items. Please try again.');
        }
      });
    }
  });
});
// This is the correct way to get the ajax_url.  Do not hardcode it.
var wc_ajax_params = { //Correct way to define wc_ajax_params
  'ajax_url': '<?php echo admin_url( 'admin-ajax.php' ); ?>'
};


document.getElementById('apply_coupon').addEventListener('click', function() {
  let couponCode = document.getElementById('coupon_code').value.trim();
  let messageBox = document.getElementById('coupon_message');

  if (!couponCode) {
    messageBox.textContent = "Please enter a coupon code.";
    messageBox.style.color = "red";
    return;
  }

  let data = new FormData();
  data.append('action', 'apply_coupon');
  data.append('coupon_code', couponCode);

  fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
      method: 'POST',
      body: data
    })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        messageBox.textContent = "Coupon applied successfully!";
        messageBox.style.color = "green";
        location.reload(); // Reload cart to reflect discount
      } else {
        messageBox.textContent = result.message;
        messageBox.style.color = "red";
      }
    })
    .catch(error => console.error('Error:', error));
});
</script>